name: Accept <PERSON><PERSON>, Fix <PERSON>, and Format

on:
  workflow_dispatch: {}

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'

      - name: Configure Git, Run Tests, Update Baselines, Apply Fixes
        run: |
          git config user.email "<EMAIL>"
          git config user.name "TypeScript Bot"
          npm ci
          git rm -r --quiet tests/baselines/reference
          npx hereby runtests-parallel --ci --fix || true
          npx hereby baseline-accept
          npx hereby format
          git add ./src
          git add ./tests/baselines/reference
          git diff --cached
          git commit -m "Update Baselines, Applied Lint Fixes, and/or Formatted"
          git push
