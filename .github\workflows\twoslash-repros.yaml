name: Twoslash Code Sample Repros

on:
  schedule:
    - cron: '0 8 * * *'
  repository_dispatch:
    types: [run-twoslash-repros]
  workflow_dispatch:
    inputs:
      issue:
        description: Limits run to a single issue.
        required: false
        type: string
      bisect:
        description: If set, runs a git bisect on an existing repro. Requires 'issue' to be set. Value can be revision labels (e.g. `good v4.7.3 bad main`) or `true` to infer bisect range.
        required: false
        type: string

      # Inputs provided by the bot
      distinct_id:
        description: '(bot) A distinct ID'
        required: false
        default: ''
      source_issue:
        description: '(bot) The issue that triggered this workflow'
        required: false
        default: ''
      requesting_user:
        description: '(bot) The user who requested this workflow'
        required: false
        default: ''
      status_comment:
        description: '(bot) The comment to update with the status of this workflow'
        required: false
        default: ''

run-name: ${{ github.workflow }}${{ inputs.distinct_id && format(' (bot run {0})', inputs.distinct_id) || '' }}

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  run:
    if: ${{ github.repository == 'microsoft/TypeScript' }}
    runs-on: ubuntu-latest
    steps:
      - if: ${{ github.event.inputs.bisect }}
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          filter: blob:none # https://github.blog/2020-12-21-get-up-to-speed-with-partial-clone-and-shallow-clone/
          fetch-depth: 0 # Default is 1; need to set to 0 to get the benefits of blob:none.
      - if: ${{ !github.event.inputs.bisect }}
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - uses: microsoft/TypeScript-Twoslash-Repro-Action@master
        with:
          github-token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
          issue: ${{ github.event.inputs.issue }}
          bisect: ${{ github.event.inputs.bisect }}
