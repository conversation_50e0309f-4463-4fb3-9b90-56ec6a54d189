name: Set branch version

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: Release branch name to create
        required: true
        type: string
      package_version:
        description: Release package version
        required: true
        type: string
      core_major_minor:
        description: Release core major.minor version
        required: true
        type: string

      # Inputs provided by the bot
      distinct_id:
        description: '(bot) A distinct ID'
        required: false
        default: ''
      source_issue:
        description: '(bot) The issue that triggered this workflow'
        required: false
        default: ''
      requesting_user:
        description: '(bot) The user who requested this workflow'
        required: false
        default: ''
      status_comment:
        description: '(bot) The comment to update with the status of this workflow'
        required: false
        default: ''

run-name: ${{ github.workflow }}${{ inputs.distinct_id && format(' (bot run {0})', inputs.distinct_id) || '' }}

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          ref: ${{ inputs.branch_name }}
          token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
      # notably, this is essentially the same script as `new-release-branch.yaml` (with fewer inputs), but it assumes the branch already exists
      # do note that executing the transform below will prevent the `configurePrerelease` script from running on the source, as it makes the
      # `version` identifier no longer match the regex it uses
      # required client_payload members:
      # branch_name - the target branch
      # package_version - the full version string (eg, `3.9.1-rc` or `3.9.2`)
      # core_major_minor - the major.minor pair associated with the desired package_version (eg, `3.9` for `3.9.3`)
      - run: |
          sed -i -e 's/"version": ".*"/"version": "${{ inputs.package_version }}"/g' package.json
          sed -i -e 's/const versionMajorMinor = ".*"/const versionMajorMinor = "${{ inputs.core_major_minor }}"/g' src/compiler/corePublic.ts
          sed -i -e 's/const versionMajorMinor = ".*"/const versionMajorMinor = "${{ inputs.core_major_minor }}"/g' tests/baselines/reference/api/typescript.d.ts
          sed -i -e 's/const version\(: string\)\{0,1\} = .*;/const version = "${{ inputs.package_version }}" as string;/g' src/compiler/corePublic.ts
          npm ci
          npm install # update package-lock.json to ensure the version bump is included
          npx hereby LKG
          npm test
          git diff
          git add package.json package-lock.json
          git add src/compiler/corePublic.ts
          git add tests/baselines/reference/api/typescript.d.ts
          git add --force ./lib
          git config user.email "<EMAIL>"
          git config user.name "TypeScript Bot"
          git commit -m 'Bump version to ${{ inputs.package_version }} and LKG'
          git push

      - uses: microsoft/typescript-bot-test-triggerer/.github/actions/post-workflow-result@master
        if: ${{ !cancelled() && inputs.distinct_id }}
        with:
          success_comment: "I've set the version of ${{ inputs.branch_name }} to ${{ inputs.package_version }} for you."
          failure_comment: 'I was unable set the version.'
          github_token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
          distinct_id: ${{ inputs.distinct_id }}
          source_issue: ${{ inputs.source_issue }}
          requesting_user: ${{ inputs.requesting_user }}
          status_comment: ${{ inputs.status_comment }}
