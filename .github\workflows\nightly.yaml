name: Publish Nightly

on:
  schedule:
    - cron: '0 7 * * *'
  # enable users to manually trigger with workflow_dispatch
  workflow_dispatch: {}

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  test:
    runs-on: ubuntu-latest
    if: github.repository == 'microsoft/TypeScript'

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
      - name: Setup and test nightly
        run: |
          npm ci
          npx hereby configure-nightly
          npm test

  publish:
    needs: [test]
    runs-on: ubuntu-latest
    if: github.repository == 'microsoft/TypeScript'

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
          # Use NODE_AUTH_TOKEN environment variable to authenticate to this registry.
          registry-url: https://registry.npmjs.org/
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
      - name: Setup and publish nightly
        run: |
          npm whoami
          npm ci
          npx hereby configure-nightly
          npx hereby LKG
          node ./scripts/addPackageJsonGitHead.mjs package.json
          npm publish --tag next
        env:
          NODE_AUTH_TOKEN: ${{secrets.npm_token}}
