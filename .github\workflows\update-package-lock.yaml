name: Update package-lock.json

on:
  schedule:
    # This is probably 6am UTC, which is 10pm PST or 11pm PDT
    # Alternatively, 6am local is also fine
    - cron: '0 6 * * *'
  workflow_dispatch: {}

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'microsoft/TypeScript'

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version

      - name: Update package-lock.json and push
        run: |
          rm package-lock.json
          npm install

          if git diff --exit-code --name-only package-lock.json; then
            echo "No change."
          else
            npm test
            npx hereby LKG
            git config user.email "<EMAIL>"
            git config user.name "TypeScript Bot"
            git add -f package-lock.json
            git commit -m "Update package-lock.json"
            git push
          fi
