name: Sync Two Wiki Repos

on: [gollum]

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Get repo name
        run: R=${GITHUB_REPOSITORY%?wiki}; echo "BASENAME=${R##*/}" >> $GITHUB_ENV
      - name: Checkout ${{ env.BASENAME }}-wiki
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          repository: '${{ GITHUB.repository_owner }}/${{ env.BASENAME }}-wiki'
          token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
          fetch-depth: 0
      - name: Run sync
        run: ./.github/workflows/sync
        env:
          PUSHER: typescript-bot <<EMAIL>>
          AUTH: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
