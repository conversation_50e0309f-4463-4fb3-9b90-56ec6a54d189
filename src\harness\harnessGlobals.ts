import * as chai from "chai";

import * as ts from "./_namespaces/ts.js";

// this will work in the browser via browserify
declare global {
    // Module transform: converted from ambient declaration
    var assert: typeof chai.assert;
}
declare global {
    // Module transform: converted from ambient declaration
    var expect: typeof chai.expect;
}
globalThis.assert = chai.assert;
{
    // chai's builtin `assert.isFalse` is featureful but slow - we don't use those features,
    // so we'll just overwrite it as an alterative to migrating a bunch of code off of chai
    assert.isFalse = (expr: any, msg: string) => {
        if (expr !== false) throw new Error(msg);
    };

    const assertDeepImpl = assert.deepEqual;
    assert.deepEqual = (a, b, msg) => {
        if (ts.isArray(a) && ts.isArray(b)) {
            assertDeepImpl(arrayExtraKeysObject(a), arrayExtraKeysObject(b), "Array extra keys differ");
        }
        assertDeepImpl(a, b, msg);

        function arrayExtraKeysObject(a: readonly unknown[]): object {
            const obj: { [key: string]: unknown; } = {};
            for (const key in a) {
                if (Number.isNaN(Number(key))) {
                    obj[key] = a[key];
                }
            }
            return obj;
        }
    };
}
globalThis.expect = chai.expect;

// empty ts namespace so this file is included in the `ts.ts` namespace file generated by the module swapover
// This way, everything that ends up importing `ts` downstream also imports this file and picks up its augmentation
