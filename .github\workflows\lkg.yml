name: Update LKG

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: Release branch name to LKG
        required: true
        type: string

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - run: |
          if [[ ! "${{ inputs.branch_name }}" =~ ^release- ]]; then
            echo "Branch name must start with 'release-'"
            exit 1
          fi

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          ref: ${{ inputs.branch_name }}
          token: ${{ secrets.TS_BOT_GITHUB_TOKEN }}
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
      - run: |
          npm ci
          npx hereby LKG
          git add --force ./lib
          git config user.email "<EMAIL>"
          git config user.name "TypeScript Bot"
          git commit -m 'Update LKG'
          git push
