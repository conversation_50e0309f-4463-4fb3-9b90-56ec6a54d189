name: Create Releasable Package Drop

on:
  push:
    branches:
      - release-*

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
      - name: npm install and test
        run: |
          npm ci
          npm test
      - name: Installing browsers
        run: npx playwright install --with-deps
      - name: Validate the browser can import TypeScript
        run: npx hereby test-browser-integration
      - name: <PERSON>K<PERSON>, clean, and pack
        run: |
          npx hereby LKG
          npx hereby clean
          node ./scripts/addPackageJsonGitHead.mjs package.json
          npm pack ./
          mv typescript-*.tgz typescript.tgz
      - name: Upload built tarfile
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: tgz
          path: typescript.tgz
