name: CI

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    branches:
      - main
      - release-*
  merge_group:
    branches:
      - main
      # - release-*

permissions:
  contents: read

# Ensure scripts are run with pipefail. See:
# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#exit-codes-and-error-action-preference
defaults:
  run:
    shell: bash

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        config:
          # Main builds
          - os: ubuntu-latest
            node-version: '24'
            bundle: true

          # Other builds (skipped in merge queues)
          - os: windows-latest
            node-version: '24'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          - os: macos-latest
            node-version: '24'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          - os: ubuntu-latest
            node-version: '22'
            bundle: true
          - os: windows-latest
            node-version: '22'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          # Skip macOS for this version; resources are limited.
          # - os: macos-latest
          #   node-version: '22'
          #   bundle: true
          #   skip: ${{ github.event_name == 'merge_group' }}
          - os: ubuntu-latest
            node-version: '20'
            bundle: true
          - os: windows-latest
            node-version: '20'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          # Skip macOS for this version; resources are limited.
          # - os: macos-latest
          #   node-version: '20'
          #   bundle: true
          #   skip: ${{ github.event_name == 'merge_group' }}
          - os: ubuntu-latest
            node-version: '18'
            bundle: true
          - os: windows-latest
            node-version: '18'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          # Skip macOS for this version; resources are limited.
          # - os: macos-latest
          #   node-version: '18'
          #   bundle: true
          #   skip: ${{ github.event_name == 'merge_group' }}
          - os: ubuntu-latest
            node-version: '16'
            bundle: true
          - os: windows-latest
            node-version: '16'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          - os: macos-latest
            node-version: '16'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          - os: ubuntu-latest
            node-version: '14'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          - os: windows-latest
            node-version: '14'
            bundle: true
            skip: ${{ github.event_name == 'merge_group' }}
          # No Node 14 on ARM macOS
          # - os: macos-latest
          #   node-version: '14'
          #   bundle: true
          #   skip: ${{ github.event_name == 'merge_group' }}

          - os: ubuntu-latest
            node-version: 'lts/*'
            bundle: false
            skip: ${{ github.event_name == 'merge_group' }}

        exclude:
          - config:
              skip: true

    runs-on: ${{ matrix.config.os }}
    name: Test Node ${{ matrix.config.node-version }} on ${{ matrix.config.os }}${{ (!matrix.config.bundle && ' with --no-bundle') || '' }}

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - name: Use node version ${{ matrix.config.node-version }}
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: ${{ matrix.config.node-version }}
          check-latest: true
      - run: npm ci

      - name: Tests
        id: test
        # run tests, but lint separately
        run: npm run test -- --no-lint --bundle=${{ matrix.config.bundle }}

      - name: Print baseline diff on failure
        if: ${{ failure() && steps.test.conclusion == 'failure' }}
        run: |
          npx hereby baseline-accept
          git add tests/baselines/reference
          git diff --staged --exit-code

  coverage:
    if: ${{ github.event_name != 'merge_group' }}

    runs-on:
      - 'self-hosted'
      - '1ES.Pool=TypeScript-1ES-GitHub-Large'
      - '1ES.ImageOverride=mariner-2.0'

    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Run tests with coverage
        run: npm test -- --no-lint --coverage

      - name: Upload coverage artifact
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: coverage
          path: coverage

      - uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
        with:
          use_oidc: ${{ !(github.event_name == 'pull_request' && github.event.pull_request.head.repo.fork) }}
          disable_search: true
          files: ./coverage/codecov.json

  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Linter
        run: npm run lint

  knip:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Unused exports
        run: npm run knip

  format:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - uses: actions/cache@0400d5f644dc74513175e3cd8d07132dd4860809 # v4.2.4
        with:
          path: ~/.cache/dprint
          key: ${{ runner.os }}-dprint-${{ hashFiles('package-lock.json', '.dprint.jsonc') }}
          restore-keys: |
            ${{ runner.os }}-dprint-

      - name: Check formatting
        run: npx dprint check

  browser-integration:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Installing browsers
        run: npx playwright install --with-deps

      - name: Validate the browser can import TypeScript
        run: npx hereby test-browser-integration

  typecheck:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Build src
        run: npx hereby build-src

  smoke:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version

      - run: npm ci

      - run: npx hereby lkg
      - run: |
          node ./scripts/addPackageJsonGitHead.mjs package.json
          npm pack
          mv typescript*.tgz typescript.tgz
          echo "package=$PWD/typescript.tgz" >> "$GITHUB_OUTPUT"
        id: pack

      - name: Smoke test
        run: |
          cd "$(mktemp -d)"
          npm init --yes
          npm install ${{ steps.pack.outputs.package }}

          echo "Testing tsc..."
          npx tsc --version

          echo "Testing tsserver..."
          echo '{"seq": 1, "command": "status"}' | npx tsserver

          node $GITHUB_WORKSPACE/scripts/checkModuleFormat.mjs typescript
          node $GITHUB_WORKSPACE/scripts/checkModuleFormat.mjs typescript/lib/tsserverlibrary

  package-size:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          path: pr

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          path: base
          ref: ${{ github.base_ref }}

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: |
          npm --version
          # corepack enable npm

      - run: |
          npm install -g $(jq -r '.packageManager' < package.json)
          npm --version
        working-directory: ./pr

      - run: npm ci
        working-directory: ./pr

      - run: npm ci
        working-directory: ./base

      - run: npx hereby lkg
        working-directory: ./pr

      - run: npx hereby lkg
        working-directory: ./base

      - run: |
          echo "See $GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID for more info."
          node ./pr/scripts/checkPackageSize.mjs ./base ./pr >> $GITHUB_STEP_SUMMARY

  misc:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Build scripts
        run: npx hereby scripts

      - name: ESLint tests
        run: npx hereby run-eslint-rules-tests

  self-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Build tsc
        run: npx hereby tsc

      - name: Clean
        run: npx hereby clean-src

      - name: Self build
        run: npx hereby build-src --built

  baselines:
    if: ${{ github.event_name != 'merge_group' }}

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 'lts/*'
      - run: npm ci

      - name: Remove all baselines
        run: rm -rf tests/baselines/reference

      - name: Run tests
        run: npm test &> /dev/null || exit 0

      - name: Accept baselines
        run: |
          npx hereby baseline-accept
          git add tests/baselines/reference

      - name: Check baselines
        id: check-baselines
        run: |
          function print_diff() {
            if ! git diff --staged --exit-code --quiet --diff-filter=$1; then
              echo "$2:"
              git diff --staged --name-only --diff-filter=$1
            fi
          }

          if ! git diff --staged --exit-code --quiet; then
            print_diff ACR "Missing baselines"
            print_diff MTUXB "Modified baselines"
            print_diff D "Unused baselines"
            git diff --staged > fix_baselines.patch
            exit 1
          fi

      - name: Upload baseline diff artifact
        if: ${{ failure() && steps.check-baselines.conclusion == 'failure' }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: fix_baselines.patch
          path: fix_baselines.patch

  required:
    runs-on: ubuntu-latest
    if: ${{ always() }}
    needs:
      - test
      - coverage
      - lint
      - knip
      - format
      - browser-integration
      - typecheck
      - smoke
      - package-size
      - misc
      - self-check
      - baselines

    steps:
      - name: Check required jobs
        env:
          NEEDS: ${{ toJson(needs) }}
        run: |
          ! echo $NEEDS | jq -e 'to_entries[] | { job: .key, result: .value.result } | select((.result == "success" or .result == "skipped") | not)'
